package com.pass.hbl.manager.backend.persistence.repository.hm;

import com.pass.hbl.manager.backend.persistence.domain.admin.AdminLeagueMembersCountDO;
import com.pass.hbl.manager.backend.persistence.domain.hm.*;
import com.pass.hbl.manager.backend.persistence.entity.hm.HmLeague;
import com.pass.hbl.manager.backend.persistence.entity.hm.HmLeagueMembership;
import com.pass.hbl.manager.backend.persistence.entity.hm.HmUserProfile;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.PagingAndSortingRepository;
import org.springframework.data.repository.query.Param;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;

public interface HmLeagueMembershipRepository extends PagingAndSortingRepository<HmLeagueMembership, UUID> {

    @Query(value = "select Cast(id as varchar) from hm.league_membership where id in :ids and deleted = true", nativeQuery = true)
    List<String> findNotExistingByIdIn(@Param("ids") Set<UUID> ids);

    @Query("select m.id, m.userProfile.id, m.league.id from HmLeagueMembership m where m.league.season.id = :seasonId and m.deleted = false")
    List<UUID[]> findAllIdsAndUserProfileIdsAndLeagueIds(@Param("seasonId") UUID seasonId);

    @Query("select m.id, m.userProfile.id, m.league.id from HmLeagueMembership m where m.league.id in :leagueIds and m.deleted = false")
    List<UUID[]> findAllIdsAndUserProfileIdsAndLeagueIdsByLeagueIdIn(@Param("leagueIds") List<UUID> leagueIds);

    @Query("select m.id, m.userProfile.id, m.joined, m.score, m.balance from HmLeagueMembership m where m.league.id = :leagueId and m.deleted = false")
    List<Object[]> findAllMembershipInfoByLeagueId(@Param("leagueId") UUID leagueId);

    @Query("select m.id as id, m.userProfile.id as userId, m.league.id as leagueId, m.league.name as leagueName, m.score as score, m.createdAt as createdAt from HmLeagueMembership m where m.league.season.id = :seasonId and m.deleted = false")
    List<HmLeagueMembershipScoreDO> findAllMembershipInfoBySeason(@Param("seasonId") UUID seasonId);

    @Query("select m.id as id, m.userProfile.id as userId, m.league.id as leagueId, m.league.name as leagueName, m.score as score, m.createdAt as createdAt " +
            "from HmLeagueMembership m where m.league.season.id = :seasonId and not(m.league.id in :excludedLeagues) and m.deleted = false")
    List<HmLeagueMembershipScoreDO> findMembershipInfoBySeasonAndLeagueIdNotIn(@Param("seasonId") UUID seasonId, @Param("excludedLeagues") Set<UUID> excludedLeagues);

    @Query("select m.id as id, m.userProfile.id as memberId, m.league.id as leagueId from HmLeagueMembership m where m.league.id in :leagueIds and m.deleted = false")
    List<HmLeagueMembershipBaseDO> findAllMembershipsByLeagueIdIn(@Param("leagueIds") List<UUID> leagueIds);

    @Query("select m.league.id from HmLeagueMembership m where m.userProfile.id = :userId and m.league.season.id = :seasonId and m.deleted = false")
    Set<UUID> findLeagueIdsByUserProfileIdAndSeasonId(@Param("userId") UUID userId, @Param("seasonId") UUID seasonId);

    @Query("select m.league.id from HmLeagueMembership m where m.userProfile.id = :userId and m.deleted = false")
    Set<UUID> findLeagueIdsByUserProfileId(@Param("userId") UUID userId);

    List<HmLeagueMembership> findByLeagueSeasonIdAndUserProfile(UUID seasonId, HmUserProfile profile);

    @Query("""
           SELECT m.id as id,
                  m.league.id as leagueId,
                  m.league.name as leagueName,
                  m.league.owner.id as leagueOwnerId,
                  m.userProfile.id as userId,
                  m.joined as joined,
                  m.score as score,
                  m.balance as balance,
                  m.deleted as deleted
           FROM HmLeagueMembership m
           WHERE m.league.season.id = :seasonId
           AND m.userProfile.id = :userId
           """)
    List<LeagueMembershipStatsDO> findByLeagueSeasonIdAndUserProfileId(@Param("seasonId") UUID seasonId, @Param("userId") UUID userId);

    Optional<HmLeagueMembership> findFirstByUserProfileAndLeague(HmUserProfile user, HmLeague league);

    Optional<HmLeagueMembership> findFirstByUserProfileIdAndLeagueId(UUID user, UUID league);

    @Query("select m.id from HmLeagueMembership m where m.userProfile.id = :userId and m.league.id = :leagueId and m.deleted = false")
    Optional<UUID> findIdsByUserProfileIdAndLeagueId(@Param("userId") UUID userId, @Param("leagueId") UUID leagueId);


    boolean existsByUserProfileIdAndLeagueId(UUID user, UUID league);

    @Modifying
    @Query(value = "update HmLeagueMembership m set m.deleted = true, m.deletedAt = CURRENT_TIMESTAMP, m.onHold = false where m.league.id = :leagueId and (m.deleted = false or m.onHold = true)")
    void deleteByLeague(@Param("leagueId") UUID leagueId);

    @Modifying
    @Query(value = "update HmLeagueMembership m set m.deleted = true, m.deletedAt = CURRENT_TIMESTAMP, m.onHold = false where m.league.id in :leagueIds and (m.deleted = false or m.onHold = true)")
    int deleteByLeagueIdIn(@Param("leagueIds") List<UUID> leagueIds);

    @Modifying
    @Query(value = "update HmLeagueMembership m set m.deleted = true, m.deletedAt = CURRENT_TIMESTAMP where m.league.id = :leagueId and m.userProfile.id = :memberId and m.deleted = false")
    void deleteByLeagueAndMember(@Param("leagueId") UUID leagueId, @Param("memberId") UUID memberId);

    @Modifying
    @Query(value = "update HmLeagueMembership m set m.deleted = true, m.deletedAt = CURRENT_TIMESTAMP, m.onHold = true where m.league.id = :leagueId and m.userProfile.id = :memberId and m.deleted = false")
    void deleteAndSetOnHoldByLeagueAndMember(@Param("leagueId") UUID leagueId, @Param("memberId") UUID memberId);

    @Modifying
    @Query(value = "update hm.league_membership set modified_at = CURRENT_TIMESTAMP, on_hold = false where id in (:ids) and on_hold = true and deleted = true", nativeQuery = true)
    int resetOnHoldById(@Param("ids") List<UUID> ids);

    @Modifying
    @Query("update HmLeagueMembership m set m.score = m.score - :scoreToSubtract, m.balance = m.balance - :balanceToSubtract, m.modifiedAt = CURRENT_TIMESTAMP where m.id = :id")
    int resetScoreAndBalanceById(@Param("id") UUID id, @Param("scoreToSubtract") int scoreToSubtract, @Param("balanceToSubtract") int balanceToSubtract);

    @Modifying
    @Query("update HmLeagueMembership m set m.tempScore = m.score + :scoreByRound, m.tempBalance = m.balance + :moneyByRound, m.modifiedAt = CURRENT_TIMESTAMP where m.id = :id")
    int updateTempScoreAndBalanceById(@Param("id") UUID id, @Param("scoreByRound") int scoreByRound, @Param("moneyByRound") int moneyByRound);

    @Modifying
    @Query("update HmLeagueMembership m set m.score = m.score + :scoreByRound, m.balance = m.balance + :moneyByRound, m.modifiedAt = CURRENT_TIMESTAMP where m.id = :id")
    int updateScoreAndBalanceById(@Param("id") UUID id, @Param("scoreByRound") int scoreByRound, @Param("moneyByRound") int moneyByRound);

    @Modifying
    @Query("update HmLeagueMembership m set m.scoreBackup = m.score, m.roundClosingBalanceBackup = m.balance, m.modifiedAt = CURRENT_TIMESTAMP")
    int createScoreAndBalanceBackup();

    @Modifying
    @Query("update HmLeagueMembership m set m.tempScore = null, m.tempBalance = null, m.modifiedAt = CURRENT_TIMESTAMP " +
            "where (m.tempScore is not null or m.tempBalance is not null) and m.league.id in :leagueIds and m.deleted = false")
    int resetTempScoreAndBalance(List<UUID> leagueIds);

    @Modifying
    @Query("update HmLeagueMembership m set m.score = m.tempScore, m.balance = m.tempBalance, m.modifiedAt = CURRENT_TIMESTAMP " +
            "where ( m.tempScore is not null) and (m.tempBalance is not null) " +
            "and m.league.id in :leagueIds and m.deleted = false")
    int transferTempScoreAndBalance(List<UUID> leagueIds);

    @Query(value = "select Cast(id as varchar) from hm.league_membership where league_id in :leagueIds and deleted = false and not member_id in (select id from hm.user_profile where deleted = false)", nativeQuery = true)
    List<String> findAllByNotExistingUser(@Param("leagueIds") List<UUID> leagueIds);

    @Modifying
    @Query("update HmLeagueMembership m set m.balance = m.balance + :amount, m.modifiedAt = CURRENT_TIMESTAMP where m.league.id = :leagueId and m.userProfile.id = :userId")
    int updateBalance(@Param("leagueId") UUID leagueId, @Param("userId") UUID userId, @Param("amount") int amount);

    @Query(value = "select user_balance from hm.league_membership where league_id=:leagueId and member_id = :userId and deleted = false", nativeQuery = true)
    int getBalance(@Param("leagueId") UUID leagueId, @Param("userId") UUID userId);

    @Query(value = "select m.id as id, m.userProfile.id as memberId, m.joined as joined from HmLeagueMembership m where m.deleted = false and m.league.id = :leagueId and not m.userProfile.id = :userId order by m.joined asc")
    List<HmLeagueMembershipJoinedDO> findAllByLeagueNotEqualUserIdOrderByJoinedAsc(@Param("leagueId") UUID leagueId, @Param("userId") UUID userId);

    @Query(value = "select count(*) from hm.league_membership where league_id = :leagueId and on_hold = true and deleted = true", nativeQuery = true)
    int countOnHoldMembershipsByLeagueId(@Param("leagueId") UUID leagueId);

    @Query(value = "select Cast(id as varchar) as id, Cast(member_id as varchar) as memberId, Cast(league_id as varchar) as leagueId from hm.league_membership where member_id = :memberId and on_hold = true and deleted = true", nativeQuery = true)
    List<HmLeagueMembershipDO> findOnHoldMembershipsByUserId(@Param("memberId") UUID memberId);

    @Query(value = "select Cast(id as varchar) as id, Cast(member_id as varchar) as memberId, Cast(league_id as varchar) as leagueId from hm.league_membership where member_id = :memberId and league_id = :leagueId and on_hold = true and deleted = true order by deleted_at desc limit 1", nativeQuery = true)
    Optional<HmLeagueMembershipDO> findOnHoldMembershipByUserIdAndLeagueId(@Param("memberId") UUID memberId, @Param("leagueId") UUID leagueId);

    @Query(value = "select Cast(id as varchar) as id, Cast(member_id as varchar) as memberId, Cast(league_id as varchar) as leagueId from hm.league_membership where on_hold = true and deleted = true and (deleted_at = :date or deleted_at < :date)", nativeQuery = true)
    List<HmLeagueMembershipDO> findOnHoldMembershipsBeforeOrEqualDate(@Param("date") LocalDateTime date);

    @Modifying
    @Query(value = "update hm.league_membership set deleted = false, deleted_at = '1970-01-01 00:00:00', on_hold = false where id in :ids and on_hold = true and deleted = true", nativeQuery = true)
    int activateMembershipsByIds(@Param("ids") List<UUID> ids);

    //Projections used with custom queries for performance & concurrency reasons: specific columns are returned instead of returning the whole entity within the transactional context
    @Query(value = "select m.id as id, m.userProfile.id as memberId, m.joined as joined from HmLeagueMembership m where m.league.id = :leagueId and m.userProfile.id = :userId and m.deleted = false")
    List<HmLeagueMembershipJoinedDO> findJoinedInfoByUserIdAndLeagueId(@Param("userId") UUID userId, @Param("leagueId") UUID leagueId);

    @Modifying
    @Query(value = "update HmLeagueMembership m set m.joined = :joined where m.id = :id and m.deleted = false")
    int setJoinedDateById(@Param("id") UUID id, @Param("joined")LocalDateTime joined);

    @Query(value = """
            select cast(league_id as varchar) from hm.league_membership\s
            where league_id in (select id from hm.league where season_id = :seasonId and deleted = false)
            and deleted = false group by league_id having count(*) = :maxLeagueSize""", nativeQuery = true)
    List<String> findLeaguesWithMaxMembershipCount(UUID seasonId, int maxLeagueSize);

    @Query(value = """
            select cast(l.id as varchar) as id, l.name, count(*) as membersCount from hm.league_membership m inner join hm.league l
            on m.league_id = l.id
            where l.season_id = :seasonId and l.deleted = false and m.deleted = false
            group by l.id, l.name
            """, nativeQuery = true)
    List<AdminLeagueMembersCountDO> findMembersCountByLeague(UUID seasonId);

    /**
     * Find all league membership data for CSV export with optimized query
     * Returns all necessary fields: SSO_ID, score, balance, league_id, league_name, sso_owner_id, joined_at, league_created_at, previous_league_id
     * Uses CTE to first select league IDs for the season, then LEFT OUTER JOIN for memberships
     * Ordered by league_id and score descending for efficient rank calculation
     */
    @Query(value = """
            with season_leagues as (
                select l.id as league_id,
                       l.name as league_name,
                       l.created_at as league_created_at,
                       l.owner_id as league_owner_id,
                       l.previous_league_id as previous_league_id,
                       up.sso_id as sso_owner_id
                from hm.league l
                left outer join hm.user_profile up on l.owner_id = up.id and up.deleted = false
                where l.season_id = :seasonId
                and l.deleted = false
            )
            select up.sso_id as ssoId,
                   lm.user_score as score,
                   lm.user_balance as balance,
                   cast(lm.league_id as varchar) as leagueId,
                   sl.league_name as leagueName,
                   sl.sso_owner_id as ssoOwnerId,
                   lm.created_at as joinedAt,
                   sl.league_created_at as leagueCreatedAt,
                   cast(sl.previous_league_id as varchar) as previousLeagueId
            from hm.league_membership lm
            left outer join season_leagues sl on lm.league_id = sl.league_id
            left outer join hm.user_profile up on lm.member_id = up.id and up.deleted = false
            where lm.deleted = false
            and sl.league_id is not null
            order by lm.league_id, lm.user_score desc
            """, nativeQuery = true)
    List<LeagueMembershipCsvExportDO> findAllMembershipDataForCsvExport(@Param("seasonId") UUID seasonId);

    @Query(value = """
            SELECT COUNT(*) + 1 FROM hm.league_membership
            WHERE league_id = :leagueId
            AND deleted = false
            AND user_score > (SELECT user_score FROM hm.league_membership WHERE id = :membershipId and deleted = false)
            """, nativeQuery = true)
    int calculateUserRankInLeague(@Param("leagueId") UUID leagueId, @Param("membershipId") UUID membershipId);
}
