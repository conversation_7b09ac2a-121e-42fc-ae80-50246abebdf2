package com.pass.hbl.manager.backend.restservice.controller;

import com.pass.hbl.manager.backend.persistence.dto.hm.UserStatisticsDto;
import com.pass.hbl.manager.backend.persistence.exception.EntityNotExistException;
import com.pass.hbl.manager.backend.persistence.exception.FormatException;
import com.pass.hbl.manager.backend.persistence.exception.InvalidOperationException;
import com.pass.hbl.manager.backend.persistence.exception.RateLimitExceededException;
import com.pass.hbl.manager.backend.persistence.service.hm.UserStatisticsService;
import com.pass.hbl.manager.backend.restservice.util.ApiConstants;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.ArraySchema;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springdoc.core.converters.models.PageableAsQueryParam;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.data.domain.Pageable;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.List;

import static com.pass.hbl.manager.backend.persistence.util.Constants.DEFAULT_STAT_EXTERNAL_CLIENT;
import static com.pass.hbl.manager.backend.persistence.util.Constants.X_RATE_LIMIT_RESET;

@RestController
@RequestMapping(ApiConstants.STAT_API + "/user")
@Validated
@Tag(name = "statistics", description = "API for user statistics data")
@Slf4j
public class UserStatisticsController {

    private final UserStatisticsService userStatisticsService;

    public UserStatisticsController(UserStatisticsService userStatisticsService) {
        this.userStatisticsService = userStatisticsService;
    }


    @Operation(summary = "Get all user statistics")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Success",
                    content = {@Content(mediaType = MediaType.APPLICATION_JSON_VALUE,
                            array = @ArraySchema(schema = @Schema(implementation = UserStatisticsDto.class)))}),
            @ApiResponse(responseCode = "401", description = "Unauthorized", content = {@Content}),
            @ApiResponse(responseCode = "429", description = "Rate limit exceeded", content = {@Content})
    })
    @GetMapping(value = "/details", produces = MediaType.APPLICATION_JSON_VALUE)
    @PageableAsQueryParam
    public List<UserStatisticsDto> getAllUserStatistics(
            @Parameter(description = "Filter users modified after this date")
            @RequestParam(required = false)
            @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
            LocalDateTime changedAfter,
            Pageable pageable,
            HttpServletRequest request,
            HttpServletResponse response
    ) throws IOException {

        String requestUrl = request.getRequestURI();
        // This ensures consistent tracking and rate limiting for all statistics requests for same external client "client_stat"
        String externalClient = DEFAULT_STAT_EXTERNAL_CLIENT;

        log.info("Received request for all user statistics from client: {} with URL: {}", externalClient, requestUrl);

        try {
            return userStatisticsService.getAllUserStatistics(changedAfter, pageable, requestUrl, externalClient);
        } catch (RateLimitExceededException rateLimitExceededException) {
            response.setStatus(HttpStatus.TOO_MANY_REQUESTS.value());
            response.getWriter().write(rateLimitExceededException.getMessage());
            // Time when rate limit will be reset
            response.setHeader(X_RATE_LIMIT_RESET, Long.toString(rateLimitExceededException.getRateLimitResetEpoch()));
            return null;
        }
    }


    @Operation(summary = "Get all users who updated their lineup after a given date until the request time")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Success",
                    content = {@Content(mediaType = MediaType.APPLICATION_JSON_VALUE,
                            array = @ArraySchema(schema = @Schema(implementation = UserStatisticsDto.class)))}),
            @ApiResponse(responseCode = "401", description = "Unauthorized", content = {@Content}),
            @ApiResponse(responseCode = "429", description = "Rate limit exceeded", content = {@Content})
    })
    @GetMapping(value = "/lineupActivity", produces = MediaType.APPLICATION_JSON_VALUE)
    @PageableAsQueryParam
    public List<String> getAllUsersWithUpdatedLineup(
            @Parameter(description = "Filter users who updated their lineup after after this date")
            @RequestParam()
            @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
            LocalDateTime changedAfter,
            Pageable pageable,
            HttpServletRequest request,
            HttpServletResponse response
    ) throws IOException {

        String requestUrl = request.getRequestURI();
        // This ensures consistent tracking and rate limiting for all statistics requests for same external client "client_stat"
        String externalClient = DEFAULT_STAT_EXTERNAL_CLIENT;
        log.info("Received request for all user updated lineup from client: {} with URL: {}", externalClient, requestUrl);
        try {
            return userStatisticsService.getAllUsersWithUpdatedLineup(changedAfter, pageable, requestUrl, externalClient);
        } catch (RateLimitExceededException rateLimitExceededException) {
            response.setStatus(HttpStatus.TOO_MANY_REQUESTS.value());
            response.getWriter().write(rateLimitExceededException.getMessage());
            // Time when rate limit will be reset
            response.setHeader(X_RATE_LIMIT_RESET, Long.toString(rateLimitExceededException.getRateLimitResetEpoch()));
            return null;
        } catch (InvalidOperationException | FormatException | EntityNotExistException e) {
            throw new RuntimeException(e);
        }
    }


    @Operation(summary = "Get all users who have have performed at least one transfer market action since a given date")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Success",
                    content = {@Content(mediaType = MediaType.APPLICATION_JSON_VALUE,
                            array = @ArraySchema(schema = @Schema(implementation = UserStatisticsDto.class)))}),
            @ApiResponse(responseCode = "401", description = "Unauthorized", content = {@Content}),
            @ApiResponse(responseCode = "429", description = "Rate limit exceeded", content = {@Content})
    })
    @GetMapping(value = "/transferActivity", produces = MediaType.APPLICATION_JSON_VALUE)
    @PageableAsQueryParam
    public List<String> getAllUsersWithTransferMarketActivity(
            @Parameter(description = "Filter users who have have performed at least one transfer market action after this date")
            @RequestParam()
            @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
            LocalDateTime changedAfter,
            Pageable pageable,
            HttpServletRequest request,
            HttpServletResponse response
    ) throws IOException {
        String requestUrl = request.getRequestURI();
        // This ensures consistent tracking and rate limiting for all statistics requests for same external client "client_stat"
        String externalClient = DEFAULT_STAT_EXTERNAL_CLIENT;
        log.info("Received request for all user have performed at least one TM action from client: {} with URL: {}", externalClient, requestUrl);
        try {
            return userStatisticsService.getAllUsersWithTransferMarketActivity(changedAfter, pageable, requestUrl, externalClient);
        } catch (RateLimitExceededException rateLimitExceededException) {
            response.setStatus(HttpStatus.TOO_MANY_REQUESTS.value());
            response.getWriter().write(rateLimitExceededException.getMessage());
            // Time when rate limit will be reset
            response.setHeader(X_RATE_LIMIT_RESET, Long.toString(rateLimitExceededException.getRateLimitResetEpoch()));
            return null;
        } catch (InvalidOperationException | FormatException | EntityNotExistException e) {
            throw new RuntimeException(e);
        }
    }


    @Operation(summary = "Get all users who have listed at least one player on the transfer market since a given date")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Success",
                    content = {@Content(mediaType = MediaType.APPLICATION_JSON_VALUE,
                            array = @ArraySchema(schema = @Schema(implementation = UserStatisticsDto.class)))}),
            @ApiResponse(responseCode = "401", description = "Unauthorized", content = {@Content}),
            @ApiResponse(responseCode = "429", description = "Rate limit exceeded", content = {@Content})
    })
    @GetMapping(value = "/transferActivity/sale", produces = MediaType.APPLICATION_JSON_VALUE)
    @PageableAsQueryParam
    public List<String> getAllUserWithPlayerSales(
            @Parameter(description = "Filter users who have listed at least one player on the transfer market after this date")
            @RequestParam()
            @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
            LocalDateTime changedAfter,
            Pageable pageable,
            HttpServletRequest request,
            HttpServletResponse response
    ) throws IOException {
        String requestUrl = request.getRequestURI();
        // This ensures consistent tracking and rate limiting for all statistics requests for same external client "client_stat"
        String externalClient = DEFAULT_STAT_EXTERNAL_CLIENT;
        log.info("Received request for all user who have listed at least one player on the transfer market  from client: {} with URL: {}", externalClient, requestUrl);
        try {
            return userStatisticsService.getAllUsersWithPlayerSales(changedAfter, pageable, requestUrl, externalClient);
        } catch (RateLimitExceededException rateLimitExceededException) {
            response.setStatus(HttpStatus.TOO_MANY_REQUESTS.value());
            response.getWriter().write(rateLimitExceededException.getMessage());
            // Time when rate limit will be reset
            response.setHeader(X_RATE_LIMIT_RESET, Long.toString(rateLimitExceededException.getRateLimitResetEpoch()));
            return null;
        } catch (InvalidOperationException | FormatException | EntityNotExistException e) {
            throw new RuntimeException(e);
        }
    }


    @Operation(summary = "Get all users who have made at least one offer on the transfer market since a given date")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Success",
                    content = {@Content(mediaType = MediaType.APPLICATION_JSON_VALUE,
                            array = @ArraySchema(schema = @Schema(implementation = UserStatisticsDto.class)))}),
            @ApiResponse(responseCode = "401", description = "Unauthorized", content = {@Content}),
            @ApiResponse(responseCode = "429", description = "Rate limit exceeded", content = {@Content})
    })
    @GetMapping(value = "/transferActivity/offer", produces = MediaType.APPLICATION_JSON_VALUE)
    @PageableAsQueryParam
    public List<String> getAllUserWithSubmittedOffer(
            @Parameter(description = "Filter users who have made at least one offer on the transfer market after this date")
            @RequestParam()
            @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
            LocalDateTime changedAfter,
            Pageable pageable,
            HttpServletRequest request,
            HttpServletResponse response
    ) throws IOException {
        String requestUrl = request.getRequestURI();
        // This ensures consistent tracking and rate limiting for all statistics requests for same external client "client_stat"
        String externalClient = DEFAULT_STAT_EXTERNAL_CLIENT;
        log.info("Received request for all user who have made at least one offer on the transfer market  from client: {} with URL: {}", externalClient, requestUrl);
        try {
            return userStatisticsService.getAllUserWithSubmittedOffer(changedAfter, pageable, requestUrl, externalClient);
        } catch (RateLimitExceededException rateLimitExceededException) {
            response.setStatus(HttpStatus.TOO_MANY_REQUESTS.value());
            response.getWriter().write(rateLimitExceededException.getMessage());
            // Time when rate limit will be reset
            response.setHeader(X_RATE_LIMIT_RESET, Long.toString(rateLimitExceededException.getRateLimitResetEpoch()));
            return null;
        } catch (InvalidOperationException | FormatException | EntityNotExistException e) {
            throw new RuntimeException(e);
        }
    }


    @Operation(summary = "Get all users who have deleted their app accounts since a given date")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Success",
                    content = {@Content(mediaType = MediaType.APPLICATION_JSON_VALUE,
                            array = @ArraySchema(schema = @Schema(implementation = UserStatisticsDto.class)))}),
            @ApiResponse(responseCode = "401", description = "Unauthorized", content = {@Content}),
            @ApiResponse(responseCode = "429", description = "Rate limit exceeded", content = {@Content})
    })
    @GetMapping(value = "/accountDeleted", produces = MediaType.APPLICATION_JSON_VALUE)
    @PageableAsQueryParam
    public List<String> getAllDeletedUserAccounts(
            @Parameter(description = "Filter users who have deleted their app accounts after this date")
            @RequestParam(required = false)
            @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
            LocalDateTime changedAfter,
            Pageable pageable,
            HttpServletRequest request,
            HttpServletResponse response
    ) throws IOException {
        String requestUrl = request.getRequestURI();
        // This ensures consistent tracking and rate limiting for all statistics requests for same external client "client_stat"
        String externalClient = DEFAULT_STAT_EXTERNAL_CLIENT;
        log.info("Received request for all user who have deleted their app accounts from client: {} with URL: {}", externalClient, requestUrl);
        try {
            return userStatisticsService.getAllDeletedUserAccounts(changedAfter, pageable, requestUrl, externalClient);
        } catch (RateLimitExceededException rateLimitExceededException) {
            response.setStatus(HttpStatus.TOO_MANY_REQUESTS.value());
            response.getWriter().write(rateLimitExceededException.getMessage());
            // Time when rate limit will be reset
            response.setHeader(X_RATE_LIMIT_RESET, Long.toString(rateLimitExceededException.getRateLimitResetEpoch()));
            return null;
        } catch (InvalidOperationException | FormatException | EntityNotExistException e) {
            throw new RuntimeException(e);
        }
    }


    @Operation(summary = "Get all users who have invited at least one manager since a given date")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Success",
                    content = {@Content(mediaType = MediaType.APPLICATION_JSON_VALUE,
                            array = @ArraySchema(schema = @Schema(implementation = UserStatisticsDto.class)))}),
            @ApiResponse(responseCode = "401", description = "Unauthorized", content = {@Content}),
            @ApiResponse(responseCode = "429", description = "Rate limit exceeded", content = {@Content})
    })
    @GetMapping(value = "/leagueInvitation/activity", produces = MediaType.APPLICATION_JSON_VALUE)
    @PageableAsQueryParam
    public List<String> getAllUserWithLeagueInvitationActivity(
            @Parameter(description = "Filter users who have invited at least one manager after this date")
            @RequestParam()
            @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
            LocalDateTime changedAfter,
            Pageable pageable,
            HttpServletRequest request,
            HttpServletResponse response
    ) throws IOException {
        String requestUrl = request.getRequestURI();
        // This ensures consistent tracking and rate limiting for all statistics requests for same external client "client_stat"
        String externalClient = DEFAULT_STAT_EXTERNAL_CLIENT;
        log.info("Received request for all user who have invited at least one manager from client: {} with URL: {}", externalClient, requestUrl);
        try {
            return userStatisticsService.getAllUserWithLeagueInvitationActivity(changedAfter, pageable, requestUrl, externalClient);
        } catch (RateLimitExceededException rateLimitExceededException) {
            response.setStatus(HttpStatus.TOO_MANY_REQUESTS.value());
            response.getWriter().write(rateLimitExceededException.getMessage());
            // Time when rate limit will be reset
            response.setHeader(X_RATE_LIMIT_RESET, Long.toString(rateLimitExceededException.getRateLimitResetEpoch()));
            return null;
        } catch (InvalidOperationException | FormatException | EntityNotExistException e) {
            throw new RuntimeException(e);
        }
    }

    @Operation(summary = "Export lineup activity statistics as CSV", description = "Exports all users with their last lineup activity timestamp as CSV file")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "CSV file generated successfully"),
            @ApiResponse(responseCode = "500", description = "Internal server error")
    })
    @GetMapping(value = "/lineupActivity/csv")
    public ResponseEntity<ByteArrayResource> exportLineupActivityCsv() {
        String csvContent = userStatisticsService.generateLineupActivityCsv();

        byte[] csvBytes = csvContent.getBytes(StandardCharsets.UTF_8);
        ByteArrayResource resource = new ByteArrayResource(csvBytes);

        HttpHeaders headers = new HttpHeaders();
        headers.add(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=lineup_activity_statistics.csv");

        return ResponseEntity.ok()
                .headers(headers)
                .contentType(MediaType.APPLICATION_OCTET_STREAM)
                .body(resource);
    }

    @Operation(summary = "Export transfer market activity statistics as CSV", description = "Exports all users with their last transfer market activity timestamp as CSV file")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "CSV file generated successfully"),
            @ApiResponse(responseCode = "500", description = "Internal server error")
    })
    @GetMapping(value = "/transferActivity/csv")
    public ResponseEntity<ByteArrayResource> exportTransferMarketActivityCsv() {
        String csvContent = userStatisticsService.generateTransferMarketActivityCsv();

        byte[] csvBytes = csvContent.getBytes(StandardCharsets.UTF_8);
        ByteArrayResource resource = new ByteArrayResource(csvBytes);

        HttpHeaders headers = new HttpHeaders();
        headers.add(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=transfer_market_activity_statistics.csv");

        return ResponseEntity.ok()
                .headers(headers)
                .contentType(MediaType.APPLICATION_OCTET_STREAM)
                .body(resource);
    }

    @Operation(summary = "Export player sales activity statistics as CSV", description = "Exports all users with their last player sales activity timestamp as CSV file")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "CSV file generated successfully"),
            @ApiResponse(responseCode = "500", description = "Internal server error")
    })
    @GetMapping(value = "/transferActivity/sale/csv")
    public ResponseEntity<ByteArrayResource> exportPlayerSalesActivityCsv() {
        String csvContent = userStatisticsService.generatePlayerSalesActivityCsv();

        byte[] csvBytes = csvContent.getBytes(StandardCharsets.UTF_8);
        ByteArrayResource resource = new ByteArrayResource(csvBytes);

        HttpHeaders headers = new HttpHeaders();
        headers.add(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=player_sales_activity_statistics.csv");

        return ResponseEntity.ok()
                .headers(headers)
                .contentType(MediaType.APPLICATION_OCTET_STREAM)
                .body(resource);
    }

    @Operation(summary = "Export submitted offer activity statistics as CSV", description = "Exports all users with their last submitted offer activity timestamp as CSV file")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "CSV file generated successfully"),
            @ApiResponse(responseCode = "500", description = "Internal server error")
    })
    @GetMapping(value = "/transferActivity/offer/csv")
    public ResponseEntity<ByteArrayResource> exportSubmittedOfferActivityCsv() {
        String csvContent = userStatisticsService.generateSubmittedOfferActivityCsv();

        byte[] csvBytes = csvContent.getBytes(StandardCharsets.UTF_8);
        ByteArrayResource resource = new ByteArrayResource(csvBytes);

        HttpHeaders headers = new HttpHeaders();
        headers.add(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=submitted_offer_activity_statistics.csv");

        return ResponseEntity.ok()
                .headers(headers)
                .contentType(MediaType.APPLICATION_OCTET_STREAM)
                .body(resource);
    }

    @Operation(summary = "Export league invitation activity statistics as CSV", description = "Exports all users with their last league invitation activity timestamp as CSV file")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "CSV file generated successfully"),
            @ApiResponse(responseCode = "500", description = "Internal server error")
    })
    @GetMapping(value = "/leagueInvitation/activity/csv")
    public ResponseEntity<ByteArrayResource> exportLeagueInvitationActivityCsv() {
        String csvContent = userStatisticsService.generateLeagueInvitationActivityCsv();

        byte[] csvBytes = csvContent.getBytes(StandardCharsets.UTF_8);
        ByteArrayResource resource = new ByteArrayResource(csvBytes);

        HttpHeaders headers = new HttpHeaders();
        headers.add(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=league_invitation_activity_statistics.csv");

        return ResponseEntity.ok()
                .headers(headers)
                .contentType(MediaType.APPLICATION_OCTET_STREAM)
                .body(resource);
    }
}
