package com.pass.hbl.manager.backend.persistence.repository.hm;

import com.pass.hbl.manager.backend.persistence.domain.hm.HmDeletedLineupDO;
import com.pass.hbl.manager.backend.persistence.entity.hm.HmLeague;
import com.pass.hbl.manager.backend.persistence.entity.hm.HmLineup;
import com.pass.hbl.manager.backend.persistence.entity.hm.HmRound;
import com.pass.hbl.manager.backend.persistence.entity.hm.HmUserProfile;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.PagingAndSortingRepository;
import org.springframework.data.repository.query.Param;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;
import java.util.UUID;

public interface HmLineupRepository extends PagingAndSortingRepository<HmLineup, UUID> {

    @Query("select l.team.player.id from HmLineup l where l.team.owner.id = :owner and l.team.league.id = :leagueId and l.round.id = :roundId and l.deleted = false")
    Set<UUID> findIdsByOwnerAndLeagueIdAndRoundId(@Param("owner") UUID ownerId, @Param("leagueId") UUID leagueId, @Param("roundId") UUID round);

    @Query("select l from HmLineup l where l.team.owner.id = :owner and l.team.league.id = :leagueId and l.round.id = :roundId and l.deleted = false")
    List<HmLineup> findByOwnerIdAndLeagueIdAndRoundId(@Param("owner") UUID ownerId, @Param("leagueId") UUID leagueId, @Param("roundId") UUID round);

    @Query(value = "SELECT * FROM hm.lineup l WHERE l.team_id in :teams and l.round_id = :roundId and deleted = true and deleted_at > :date", nativeQuery = true)
    List<HmLineup> findDeletedByRoundAndTeamsAfterDate(@Param("teams") List<UUID> teams, @Param("roundId") UUID roundId, @Param("date") LocalDateTime date);

    @Query(value = "SELECT Cast(id as varchar) as id, Cast(team_id as varchar) as teamId, deleted_at as deletedAt  FROM hm.lineup l WHERE l.team_id in :teams and l.round_id = :roundId and deleted = true and deleted_at > :date", nativeQuery = true)
    List<HmDeletedLineupDO> findDeletedInfoByRoundAndTeamsAfterDate(@Param("teams") List<UUID> teams, @Param("roundId") UUID roundId, @Param("date") LocalDateTime date);

    @Modifying
    @Query("delete from HmLineup l where (l.team in (select t from HmTeam t where t.owner = :owner and t.league = :league) and l.round = :round and l.deleted = false)")
    void deleteByOwnerAndLeagueAndRound(@Param("owner") HmUserProfile owner, @Param("league") HmLeague league, @Param("round")HmRound round);

    @Modifying
    @Query("update HmLineup l set l.deleted = true, l.deletedAt = CURRENT_TIMESTAMP where (l.team in (select t from HmTeam t where t.owner.id = :ownerId and t.league.id = :leagueId) and l.deleted = false)")
    void deleteByLeagueAndOwner(@Param("leagueId") UUID leagueId, @Param("ownerId") UUID ownerId);

    @Modifying
    @Query(value = "update HmLineup l set l.deleted = true, l.deletedAt = CURRENT_TIMESTAMP where (l.team in (select t from HmTeam t where t.league.id = :leagueId) and l.deleted = false)")
    void deleteByLeague(@Param("leagueId") UUID leagueId);

    @Modifying
    @Query(value = "update HmLineup l set l.deleted = true, l.deletedAt = CURRENT_TIMESTAMP where (l.team in (select t from HmTeam t where t.owner.id = :ownerId) and l.deleted = false)")
    void deleteByOwner(@Param("ownerId") UUID ownerId);

    @Modifying
    @Query(value = "update HmLineup l set l.deleted = true, l.deletedAt = CURRENT_TIMESTAMP where l.team.id = :teamId")
    void deleteByTeamMember(@Param("teamId") UUID teamId);

    @Query(value = """
            select Cast(id as varchar) from hm.league where deleted = false and season_id = :seasonId
            and not id in (select distinct(t.league_id) from hm.lineup l\s
            \tinner join hm.team t on l.team_id = t.id\s
            \twhere round_id in (:roundIds)
            ) and created_at < :maxLeagueCreatedAt
            """, nativeQuery = true)
    List<String> getLeagueWithoutLineupsInRounds(@Param("seasonId") UUID seasonId, @Param("roundIds") List<UUID> roundIds, @Param("maxLeagueCreatedAt") LocalDateTime maxLeagueCreatedAt);

    @Query(value = "SELECT DISTINCT Cast(t.owner_id as varchar) FROM hm.lineup AS l LEFT OUTER JOIN hm.team AS t ON l.team_id = t.id WHERE l.created_at > :changedAfter", nativeQuery = true)
    List<String> findIdsLineupByModifiedAtAfter(@Param("changedAfter") LocalDateTime changedAfter);

    @Query(value = """
            SELECT up.sso_id as ssoId,
                   MAX(l.created_at) as lastActivityAt
            FROM hm.lineup l
            LEFT OUTER JOIN hm.team t ON l.team_id = t.id
            LEFT OUTER JOIN hm.user_profile up ON t.owner_id = up.id
            WHERE up.deleted = false
              AND up.sso_id <> 'NOT_ASSIGNED'
            GROUP BY up.sso_id
            ORDER BY up.sso_id
            """, nativeQuery = true)
    List<Object[]> findAllUserLineupActivityForCsvExport();
}
