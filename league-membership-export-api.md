# League Membership Export API Documentation

## Overview

The League Membership Export API provides a CSV export of all league membership data for the current season. This endpoint is designed for external statistics clients to retrieve comprehensive membership information including user scores, balances, and rankings within leagues.

## Endpoint

```
GET /api/v1/statistics/league/membership/export
```

## Authentication

This endpoint requires **dual authentication**:

1. **API Key Authentication**: Include the statistics API key in the request header
2. **Magic Token Authentication**: Include the statistics magic token in the request header

### Required Headers

```
apiKey: <statistics-api-key>
magic-token: <statistics-magic-token>
```

## Request

### HTTP Method
`GET`

### URL
`/api/v1/statistics/league/membership/export`

### Parameters
None - The endpoint automatically exports data for the current season.

### Example Request

```bash
curl --output membership.csv "https://hbl-sta.pass-consulting.com/api/v1/statistics/league/membership/export" --header "apiKey: 01939200bf6e7d129210a75194eaeb67" --header "magic-token: 9619800527" --header "Accept: text/csv"
```

## Response

### Success Response (200 OK)

**Content-Type**: `multipart/form-data`
**Content-Disposition**: `attachment; filename=league_membership_data.csv`

The response is a CSV file containing league membership data with the following structure:

#### CSV Format

| Column | Type | Description |
|--------|------|-------------|
| SSO_ID | String | User's Single Sign-On identifier |
| score | Integer | User's current score in the league |
| balance | Integer | User's current balance in the league |
| rank | Integer | User's rank within the league (1-based, calculated by score descending) |
| league_id | String | Unique identifier of the league |

#### CSV Example

```csv
SSO_ID,score,balance,rank,league_id
user123,1500,250,1,league-uuid-1
user456,1200,180,2,league-uuid-1
user789,1000,120,3,league-uuid-1
user101,2000,300,1,league-uuid-2
user102,1800,280,2,league-uuid-2
```

### Data Characteristics

- **Scope**: All leagues from the current season
- **Ordering**: Data is grouped by league and ordered by score (descending) within each league
- **Ranking**: Ranks are calculated per league, starting from 1 for the highest score
- **Encoding**: UTF-8
- **Empty Values**: Missing values are represented as empty strings or "0" for numeric fields

### Error Responses

#### 404 Not Found
```json
{
  "error": "Current season not found"
}
```
**Cause**: No active season is configured in the system.

#### 401 Unauthorized
**Cause**: Missing or invalid API key or magic token.

#### 500 Internal Server Error
**Cause**: Failed to generate CSV content or database error.

## Data Source

The endpoint retrieves data using an optimized database query that:

1. Identifies all leagues for the current season
2. Fetches membership data with user SSO IDs, scores, and balances
3. Excludes deleted leagues and memberships
4. Orders results by league ID and score (descending) for efficient rank calculation

## Performance Considerations

- The query is optimized for large datasets using CTEs (Common Table Expressions)
- Only essential fields are selected to minimize data transfer
- Results are streamed directly to CSV format without intermediate object creation
- Database indexes on `season_id`, `league_id`, and `user_score` improve query performance

## Rate Limiting

This endpoint may be subject to rate limiting. Check with your system administrator for specific limits.

## Use Cases

- **Analytics**: Export membership data for external analytics platforms
- **Reporting**: Generate comprehensive league participation reports
- **Data Migration**: Backup or transfer league membership data
- **Business Intelligence**: Feed data into BI tools for insights and dashboards

## Security Notes

- This endpoint exposes user SSO IDs - ensure proper data handling compliance
- API keys and magic tokens should be kept secure and rotated regularly
- The endpoint is restricted to authorized statistics clients only

## Implementation Details

The endpoint follows the established pattern for CSV exports in the application:

1. **Service Layer**: `LeagueStatisticsService.exportLeagueMembershipDataAsCsv()`
2. **Handler Layer**: `LeagueStatisticsHandler.exportLeagueMembershipDataAsCsv()`
3. **Repository Layer**: `HmLeagueMembershipRepository.findAllMembershipDataForCsvExport()`
4. **Response**: Returns `ResponseEntity<ByteArrayResource>` with appropriate headers

## Related Endpoints

- `GET /api/v1/statistics/league/details` - Get paginated league statistics
- `GET /api/v1/statistics/user/details` - Get user statistics data

## Support

For technical support or questions about this API, contact the development team or refer to the main API documentation.
