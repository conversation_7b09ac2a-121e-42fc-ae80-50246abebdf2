package com.pass.hbl.manager.backend.persistence.service.hm.handlers;

import com.pass.hbl.manager.backend.persistence.entity.hm.HmClientRequest;
import com.pass.hbl.manager.backend.persistence.exception.EntityNotExistException;
import com.pass.hbl.manager.backend.persistence.exception.FormatException;
import com.pass.hbl.manager.backend.persistence.exception.InvalidOperationException;
import com.pass.hbl.manager.backend.persistence.repository.hm.*;
import com.pass.hbl.manager.backend.persistence.service.admin.ParameterService;
import com.pass.hbl.manager.backend.persistence.util.ParameterDefaults;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Triple;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

import static com.pass.hbl.manager.backend.persistence.util.Constants.SYSTEM_USERNAME;
import static com.pass.hbl.manager.backend.persistence.util.ParameterDefaults.DEFAULT_PARAM_STAT_RATE_LIMITING_ACTIVE;
import static com.pass.hbl.manager.backend.persistence.util.ParameterDefaults.PARAM_STAT_RATE_LIMITING_ACTIVE;
import static java.util.Objects.isNull;

@Slf4j
@Service
@Transactional
/*
  Handles user profile operations performed by the administrator (system)
 */
public class UserStatisticsActivityHandler {

    private final HmUserProfileRepository userProfileRepository;
    private final HmTransferMarketRepository hmTransferMarketRepository;
    private final HmLeagueInvitationRepository hmLeagueInvitationRepository;
    private final HmTransferMarketBidRepository hmTransferMarketBidRepository;
    private final HmLineupRepository hmLineupRepository;
    private final ParameterService parameterService;
    private final HmClientRequestRepository clientRequestRepository;
    private final RateLimitingHandler rateLimitHandler;

    @Getter
    private int maxStatPageSize = ParameterDefaults.DEFAULT_MAX_STAT_PAGE_SIZE;

    @Getter
    private int rateLimitMinutes = ParameterDefaults.DEFAULT_PARAM_STAT_RATE_LIMIT_MINUTES;

    @Getter
    private int forceRefreshCacheAfterMinutes = ParameterDefaults.DEFAULT_PARAM_FORCE_REFRESH_CACHE_AFTER_MINUTES;

    /**
     * Cache for user IDs to support pagination = Triple <left,Middle,right> represents the user ids who have
     * performed any change (related to the requested data structure e.g. joined leagues) in this time frame.
     * right -> IDs
     * middle -> changeStart e.g. 12:00 (requested date)
     * left -> changeEnd e.g 12h30 (cache update date, client request sent at)
     */
    private Triple<LocalDateTime, LocalDateTime, List<UUID>> cacheDataLineup = Triple.of(null, null, new ArrayList<>());
    private Triple<LocalDateTime, LocalDateTime, List<UUID>> cacheDataTm = Triple.of(null, null, new ArrayList<>());
    private Triple<LocalDateTime, LocalDateTime, List<UUID>> cacheDataPlayerInTm = Triple.of(null, null, new ArrayList<>());
    private Triple<LocalDateTime, LocalDateTime, List<UUID>> cacheDataOffer = Triple.of(null, null, new ArrayList<>());
    private Triple<LocalDateTime, LocalDateTime, List<UUID>> cacheDataDeletedAccount = Triple.of(null, null, new ArrayList<>());
    private Triple<LocalDateTime, LocalDateTime, List<UUID>> cacheDataInvitedManager = Triple.of(null, null, new ArrayList<>());
    public UserStatisticsActivityHandler(HmUserProfileRepository userProfileRepository,
                                         HmTransferMarketRepository hmTransferMarketRepository, HmLeagueInvitationRepository hmLeagueInvitationRepository, HmTransferMarketBidRepository hmTransferMarketBidRepository, HmLineupRepository hmLineupRepository,
                                         ParameterService parameterService,
                                         HmClientRequestRepository clientRequestRepository,
                                         RateLimitingHandler rateLimitHandler) {
        this.userProfileRepository = userProfileRepository;
        this.hmTransferMarketRepository = hmTransferMarketRepository;
        this.hmLeagueInvitationRepository = hmLeagueInvitationRepository;
        this.hmTransferMarketBidRepository = hmTransferMarketBidRepository;
        this.hmLineupRepository = hmLineupRepository;
        this.parameterService = parameterService;
        this.clientRequestRepository = clientRequestRepository;
        this.rateLimitHandler = rateLimitHandler;
    }

    public List<String> getAllUsersWithUpdatedLineup(LocalDateTime changedAfter, Pageable pageable, String requestUrl, String externalClient) throws InvalidOperationException, FormatException, EntityNotExistException {
        log.info("getAllUsersWithUpdatedLineup: changedAfter: " + changedAfter + " pageable: " + pageable + " client: " + externalClient);
        boolean isRateLimitingActive = parameterService.getAsBoolean(PARAM_STAT_RATE_LIMITING_ACTIVE, DEFAULT_PARAM_STAT_RATE_LIMITING_ACTIVE, SYSTEM_USERNAME);
        rateLimitMinutes = parameterService.getAsInteger(ParameterDefaults.PARAM_STAT_RATE_LIMIT_MINUTES, ParameterDefaults.DEFAULT_PARAM_STAT_RATE_LIMIT_MINUTES, SYSTEM_USERNAME);
        forceRefreshCacheAfterMinutes = parameterService.getAsInteger(ParameterDefaults.PARAM_FORCE_REFRESH_CACHE_AFTER_MINUTES, ParameterDefaults.DEFAULT_PARAM_FORCE_REFRESH_CACHE_AFTER_MINUTES, SYSTEM_USERNAME);
        maxStatPageSize = parameterService.getAsInteger(ParameterDefaults.PARAM_MAX_STAT_ACTIVITY_PAGE_SIZE, ParameterDefaults.DEFAULT_MAX_STAT_ACTIVITY_PAGE_SIZE, SYSTEM_USERNAME);

        // Find the existing request for this client and URL
        Optional<HmClientRequest> existingRequestOpt = clientRequestRepository.findByRequestAndExternalClient(requestUrl, externalClient);

        if (isRateLimitingActive) {
            rateLimitHandler.checkRateLimit(changedAfter, pageable, requestUrl, externalClient, existingRequestOpt, rateLimitMinutes);
        }
        LocalDateTime now = LocalDateTime.now();
        boolean forceRefreshCache = !isNull(cacheDataLineup.getLeft()) && now.isAfter(cacheDataLineup.getLeft().plusMinutes(forceRefreshCacheAfterMinutes));

        // Check if we need to refresh the cache based on multiple conditions:
        // 1. Cache hasn't been initialized yet (cacheData.getLeft() == null) -> User Ids list is empty
        // 2. Filter parameter changed (cacheData.getMiddle() != changedAfter) -> changeStart-date was changed
        // 3. forceRefreshCache if "forceRefreshCacheAfterMinutes" minutes passed -> Cache expiration time (in minutes)
        boolean refreshCache = cacheDataLineup.getLeft() == null || cacheDataLineup.getMiddle() == null || !Objects.equals(cacheDataLineup.getMiddle(), changedAfter) || forceRefreshCache;

        // Refresh the cache if needed
        if (refreshCache) {
            log.info("getAllUsersWithUpdatedLineup: refreshing the cache for users who have updated their lineup since: " + changedAfter);
            List<String> userIds;
            userIds = hmLineupRepository.findIdsLineupByModifiedAtAfter(changedAfter);
            List<UUID> uuids = userIds.stream()
                    .map(UUID::fromString)
                    .toList();
            cacheDataLineup = Triple.of(LocalDateTime.now(), changedAfter, uuids);
        }

        // If pageable size is > maxStatPageSize, use maxStatPageSize instead
        int pageSize = pageable.getPageSize() > maxStatPageSize ? maxStatPageSize : pageable.getPageSize();
        int pageNumber = pageable.getPageNumber();
        // Calculate pagination
        int startIndex = pageNumber * pageSize;
        int endIndex = Math.min(startIndex + pageSize, cacheDataLineup.getRight().size());
        // Check if the requested page is valid
        if (startIndex >= cacheDataLineup.getRight().size()) {
            return Collections.emptyList(); // Return empty list if page is out of bounds
        }
        List<UUID> pageUserIds = cacheDataLineup.getRight().subList(startIndex, endIndex);
        return userProfileRepository.findSSoIdUserByIdIn(pageUserIds);
    }

    public List<String> getAllUsersWithTransferMarketActivity(LocalDateTime changedAfter, Pageable pageable, String requestUrl, String externalClient) throws InvalidOperationException, FormatException, EntityNotExistException {
        log.info("getAllUsersWithTransferMarketActivity: changedAfter: " + changedAfter + " pageable: " + pageable + " client: " + externalClient);
        boolean isRateLimitingActive = parameterService.getAsBoolean(PARAM_STAT_RATE_LIMITING_ACTIVE, DEFAULT_PARAM_STAT_RATE_LIMITING_ACTIVE, SYSTEM_USERNAME);
        rateLimitMinutes = parameterService.getAsInteger(ParameterDefaults.PARAM_STAT_RATE_LIMIT_MINUTES, ParameterDefaults.DEFAULT_PARAM_STAT_RATE_LIMIT_MINUTES, SYSTEM_USERNAME);
        forceRefreshCacheAfterMinutes = parameterService.getAsInteger(ParameterDefaults.PARAM_FORCE_REFRESH_CACHE_AFTER_MINUTES, ParameterDefaults.DEFAULT_PARAM_FORCE_REFRESH_CACHE_AFTER_MINUTES, SYSTEM_USERNAME);
        maxStatPageSize = parameterService.getAsInteger(ParameterDefaults.PARAM_MAX_STAT_ACTIVITY_PAGE_SIZE, ParameterDefaults.DEFAULT_MAX_STAT_ACTIVITY_PAGE_SIZE, SYSTEM_USERNAME);

        // Find the existing request for this client and URL
        Optional<HmClientRequest> existingRequestOpt = clientRequestRepository.findByRequestAndExternalClient(requestUrl, externalClient);

        if (isRateLimitingActive) {
            rateLimitHandler.checkRateLimit(changedAfter, pageable, requestUrl, externalClient, existingRequestOpt, rateLimitMinutes);
        }

        LocalDateTime now = LocalDateTime.now();
        boolean forceRefreshCache = !isNull(cacheDataTm.getLeft()) && now.isAfter(cacheDataTm.getLeft().plusMinutes(forceRefreshCacheAfterMinutes));

        // Check if we need to refresh the cache based on multiple conditions:
        // 1. Cache hasn't been initialized yet (cacheData.getLeft() == null) -> User Ids list is empty
        // 2. Filter parameter changed (cacheData.getMiddle() != changedAfter) -> changeStart-date was changed
        // 3. forceRefreshCache if "forceRefreshCacheAfterMinutes" minutes passed -> Cache expiration time (in minutes)
        boolean refreshCache = cacheDataTm.getLeft() == null || cacheDataTm.getMiddle() == null || !Objects.equals(cacheDataTm.getMiddle(), changedAfter) || forceRefreshCache;

        // Refresh the cache if needed
        if (refreshCache) {
            log.info("getAllUsersWithTransferMarketActivity: refreshing the cache for users who have performed at least one Transfer Market action since: " + changedAfter);
                UUID systemId= UUID.fromString(userProfileRepository.findIdByUsername(SYSTEM_USERNAME));
                List<String> bidderIds = hmTransferMarketBidRepository.findAllUsersWithTransferMarketBidActivityAfter(changedAfter,systemId);
                List<String> ownerIds = hmTransferMarketRepository.findAllUsersWithTransferMarketActivityAfter(changedAfter,systemId);
                List<String> ownerIdsRejectBid = hmTransferMarketBidRepository.findAllOwnersWithRejectedTransferMarketBidsAfter(changedAfter,systemId);
                Set<String> userIds = new HashSet<>();
                userIds.addAll(bidderIds);
                userIds.addAll(ownerIds);
                userIds.addAll(ownerIdsRejectBid);
            List<UUID> uuids = userIds.stream()
                    .map(UUID::fromString)
                    .toList();
            cacheDataTm = Triple.of(LocalDateTime.now(), changedAfter, uuids);
        }

        // If pageable size is > maxStatPageSize, use maxStatPageSize instead
        int pageSize = pageable.getPageSize() > maxStatPageSize ? maxStatPageSize : pageable.getPageSize();
        int pageNumber = pageable.getPageNumber();
        // Calculate pagination
        int startIndex = pageNumber * pageSize;
        int endIndex = Math.min(startIndex + pageSize, cacheDataTm.getRight().size());

        // Check if the requested page is valid
        if (startIndex >= cacheDataTm.getRight().size()) {
            return Collections.emptyList(); // Return empty list if page is out of bounds
        }
        List<UUID> pageUserIds = cacheDataTm.getRight().subList(startIndex, endIndex);
        return userProfileRepository.findSSoIdUserByIdIn(pageUserIds);
    }

    public List<String> getAllUsersWithPlayerSales(LocalDateTime changedAfter, Pageable pageable, String requestUrl, String externalClient) throws InvalidOperationException, FormatException, EntityNotExistException {
        log.info("getAllUsersWithPlayerSales: changedAfter: " + changedAfter + " pageable: " + pageable + " client: " + externalClient);
        boolean isRateLimitingActive = parameterService.getAsBoolean(PARAM_STAT_RATE_LIMITING_ACTIVE, DEFAULT_PARAM_STAT_RATE_LIMITING_ACTIVE, SYSTEM_USERNAME);
        rateLimitMinutes = parameterService.getAsInteger(ParameterDefaults.PARAM_STAT_RATE_LIMIT_MINUTES, ParameterDefaults.DEFAULT_PARAM_STAT_RATE_LIMIT_MINUTES, SYSTEM_USERNAME);
        forceRefreshCacheAfterMinutes = parameterService.getAsInteger(ParameterDefaults.PARAM_FORCE_REFRESH_CACHE_AFTER_MINUTES, ParameterDefaults.DEFAULT_PARAM_FORCE_REFRESH_CACHE_AFTER_MINUTES, SYSTEM_USERNAME);
        maxStatPageSize = parameterService.getAsInteger(ParameterDefaults.PARAM_MAX_STAT_ACTIVITY_PAGE_SIZE, ParameterDefaults.DEFAULT_MAX_STAT_ACTIVITY_PAGE_SIZE, SYSTEM_USERNAME);

        // Find the existing request for this client and URL
        Optional<HmClientRequest> existingRequestOpt = clientRequestRepository.findByRequestAndExternalClient(requestUrl, externalClient);

        if (isRateLimitingActive) {
            rateLimitHandler.checkRateLimit(changedAfter, pageable, requestUrl, externalClient, existingRequestOpt, rateLimitMinutes);
        }

        LocalDateTime now = LocalDateTime.now();
        boolean forceRefreshCache = !isNull(cacheDataPlayerInTm.getLeft()) && now.isAfter(cacheDataPlayerInTm.getLeft().plusMinutes(forceRefreshCacheAfterMinutes));

        // Check if we need to refresh the cache based on multiple conditions:
        // 1. Cache hasn't been initialized yet (cacheData.getLeft() == null) -> User Ids list is empty
        // 2. Filter parameter changed (cacheData.getMiddle() != changedAfter) -> changeStart-date was changed
        // 3. forceRefreshCache if "forceRefreshCacheAfterMinutes" minutes passed -> Cache expiration time (in minutes)
        boolean refreshCache = cacheDataPlayerInTm.getLeft() == null || cacheDataPlayerInTm.getMiddle() == null || !Objects.equals(cacheDataPlayerInTm.getMiddle(), changedAfter) || forceRefreshCache;

        // Refresh the cache if needed
        if (refreshCache) {
            log.info("getAllUsersWithPlayerSales: refreshing the cache for users who have listed at least one player on the transfer market since: " + changedAfter);
            List<String> userIds;
            UUID systemId= UUID.fromString(userProfileRepository.findIdByUsername(SYSTEM_USERNAME));
            userIds = hmTransferMarketRepository.findAllUserWithPlayerSalesAfter(changedAfter,systemId);
            List<UUID> uuids = userIds.stream()
                    .map(UUID::fromString)
                    .toList();
            cacheDataPlayerInTm = Triple.of(LocalDateTime.now(), changedAfter, uuids);
        }

        // If pageable size is > maxStatPageSize, use maxStatPageSize instead
        int pageSize = pageable.getPageSize() > maxStatPageSize ? maxStatPageSize : pageable.getPageSize();
        int pageNumber = pageable.getPageNumber();
        // Calculate pagination
        int startIndex = pageNumber * pageSize;
        int endIndex = Math.min(startIndex + pageSize, cacheDataPlayerInTm.getRight().size());

        // Check if the requested page is valid
        if (startIndex >= cacheDataPlayerInTm.getRight().size()) {
            return Collections.emptyList(); // Return empty list if page is out of bounds
        }
        List<UUID> pageUserIds = cacheDataPlayerInTm.getRight().subList(startIndex, endIndex);
        return userProfileRepository.findSSoIdUserByIdIn(pageUserIds);
    }

    public List<String> getAllUserWithSubmittedOffer(LocalDateTime changedAfter, Pageable pageable, String requestUrl, String externalClient) throws InvalidOperationException, FormatException, EntityNotExistException {
        log.info("getAllUserWithSubmittedOffer: changedAfter: " + changedAfter + " pageable: " + pageable + " client: " + externalClient);
        boolean isRateLimitingActive = parameterService.getAsBoolean(PARAM_STAT_RATE_LIMITING_ACTIVE, DEFAULT_PARAM_STAT_RATE_LIMITING_ACTIVE, SYSTEM_USERNAME);
        rateLimitMinutes = parameterService.getAsInteger(ParameterDefaults.PARAM_STAT_RATE_LIMIT_MINUTES, ParameterDefaults.DEFAULT_PARAM_STAT_RATE_LIMIT_MINUTES, SYSTEM_USERNAME);
        forceRefreshCacheAfterMinutes = parameterService.getAsInteger(ParameterDefaults.PARAM_FORCE_REFRESH_CACHE_AFTER_MINUTES, ParameterDefaults.DEFAULT_PARAM_FORCE_REFRESH_CACHE_AFTER_MINUTES, SYSTEM_USERNAME);
        maxStatPageSize = parameterService.getAsInteger(ParameterDefaults.PARAM_MAX_STAT_ACTIVITY_PAGE_SIZE, ParameterDefaults.DEFAULT_MAX_STAT_ACTIVITY_PAGE_SIZE, SYSTEM_USERNAME);

        // Find the existing request for this client and URL
        Optional<HmClientRequest> existingRequestOpt = clientRequestRepository.findByRequestAndExternalClient(requestUrl, externalClient);

        if (isRateLimitingActive) {
            rateLimitHandler.checkRateLimit(changedAfter, pageable, requestUrl, externalClient, existingRequestOpt, rateLimitMinutes);
        }
        LocalDateTime now = LocalDateTime.now();
        boolean forceRefreshCache = !isNull(cacheDataOffer.getLeft()) && now.isAfter(cacheDataOffer.getLeft().plusMinutes(forceRefreshCacheAfterMinutes));

        // Check if we need to refresh the cache based on multiple conditions:
        // 1. Cache hasn't been initialized yet (cacheData.getLeft() == null) -> User Ids list is empty
        // 2. Filter parameter changed (cacheData.getMiddle() != changedAfter) -> changeStart-date was changed
        // 3. forceRefreshCache if "forceRefreshCacheAfterMinutes" minutes passed -> Cache expiration time (in minutes)
        boolean refreshCache = cacheDataOffer.getLeft() == null || cacheDataOffer.getMiddle() == null || !Objects.equals(cacheDataOffer.getMiddle(), changedAfter) || forceRefreshCache;

        // Refresh the cache if needed
        if (refreshCache) {
            log.info("getAllUserWithSubmittedOffer: refreshing the cache for users who have  performed offer at least one offer on the transfer market since: " + changedAfter);
            List<String> userIds;
            UUID systemId = UUID.fromString(userProfileRepository.findIdByUsername(SYSTEM_USERNAME));
            userIds = hmTransferMarketBidRepository.findAllUserWithSubmittedOfferAfter(changedAfter,systemId);
            List<UUID> uuids = userIds.stream()
                    .map(UUID::fromString)
                    .toList();
            cacheDataOffer = Triple.of(LocalDateTime.now(), changedAfter, uuids);
        }

        // If pageable size is > maxStatPageSize, use maxStatPageSize instead
        int pageSize = pageable.getPageSize() > maxStatPageSize ? maxStatPageSize : pageable.getPageSize();
        int pageNumber = pageable.getPageNumber();
        // Calculate pagination
        int startIndex = pageNumber * pageSize;
        int endIndex = Math.min(startIndex + pageSize, cacheDataOffer.getRight().size());

        // Check if the requested page is valid
        if (startIndex >= cacheDataOffer.getRight().size()) {
            return Collections.emptyList(); // Return empty list if page is out of bounds
        }
        List<UUID> pageUserIds = cacheDataOffer.getRight().subList(startIndex, endIndex);
        return userProfileRepository.findSSoIdUserByIdIn(pageUserIds);
    }

    public List<String> getAllDeletedUserAccounts(LocalDateTime changedAfter, Pageable pageable, String requestUrl, String externalClient) throws InvalidOperationException, FormatException, EntityNotExistException {
        log.info("getAllDeletedUserAccounts: changedAfter: " + changedAfter + " pageable: " + pageable + " client: " + externalClient);
        boolean isRateLimitingActive = parameterService.getAsBoolean(PARAM_STAT_RATE_LIMITING_ACTIVE, DEFAULT_PARAM_STAT_RATE_LIMITING_ACTIVE, SYSTEM_USERNAME);
        rateLimitMinutes = parameterService.getAsInteger(ParameterDefaults.PARAM_STAT_RATE_LIMIT_MINUTES, ParameterDefaults.DEFAULT_PARAM_STAT_RATE_LIMIT_MINUTES, SYSTEM_USERNAME);
        forceRefreshCacheAfterMinutes = parameterService.getAsInteger(ParameterDefaults.PARAM_FORCE_REFRESH_CACHE_AFTER_MINUTES, ParameterDefaults.DEFAULT_PARAM_FORCE_REFRESH_CACHE_AFTER_MINUTES, SYSTEM_USERNAME);
        maxStatPageSize = parameterService.getAsInteger(ParameterDefaults.PARAM_MAX_STAT_ACTIVITY_PAGE_SIZE, ParameterDefaults.DEFAULT_MAX_STAT_ACTIVITY_PAGE_SIZE, SYSTEM_USERNAME);

        // Find the existing request for this client and URL
        Optional<HmClientRequest> existingRequestOpt = clientRequestRepository.findByRequestAndExternalClient(requestUrl, externalClient);

        if (isRateLimitingActive) {
            rateLimitHandler.checkRateLimit(changedAfter, pageable, requestUrl, externalClient, existingRequestOpt, rateLimitMinutes);
        }

        LocalDateTime now = LocalDateTime.now();
        boolean forceRefreshCache = !isNull(cacheDataDeletedAccount.getLeft()) && now.isAfter(cacheDataDeletedAccount.getLeft().plusMinutes(forceRefreshCacheAfterMinutes));

        // Check if we need to refresh the cache based on multiple conditions:
        // 1. Cache hasn't been initialized yet (cacheData.getLeft() == null) -> User Ids list is empty
        // 2. Filter parameter changed (cacheData.getMiddle() != changedAfter) -> changeStart-date was changed
        // 3. forceRefreshCache if "forceRefreshCacheAfterMinutes" minutes passed -> Cache expiration time (in minutes)
        boolean refreshCache = cacheDataDeletedAccount.getLeft() == null || cacheDataDeletedAccount.getMiddle() == null || !Objects.equals(cacheDataDeletedAccount.getMiddle(), changedAfter) || forceRefreshCache;

        // Refresh the cache if needed
        if (refreshCache) {
            log.info("getAllDeletedUserAccounts: refreshing the cache for users who have deleted START7 account since: " + changedAfter);
            List<String> userIds;
            if (changedAfter != null) {
                userIds = userProfileRepository.findAllDeletedUserAccountsAfter(changedAfter);
            } else {
                userIds = userProfileRepository.findAllDeletedUserAccounts();
            }
            List<UUID> uuids = userIds.stream()
                    .map(UUID::fromString)
                    .toList();
            cacheDataDeletedAccount = Triple.of(LocalDateTime.now(), changedAfter, uuids);
        }

        // If pageable size is > maxStatPageSize, use maxStatPageSize instead
        int pageSize = pageable.getPageSize() > maxStatPageSize ? maxStatPageSize : pageable.getPageSize();
        int pageNumber = pageable.getPageNumber();
        // Calculate pagination
        int startIndex = pageNumber * pageSize;
        int endIndex = Math.min(startIndex + pageSize, cacheDataDeletedAccount.getRight().size());

        // Check if the requested page is valid
        if (startIndex >= cacheDataDeletedAccount.getRight().size()) {
            return Collections.emptyList(); // Return empty list if page is out of bounds
        }
        List<UUID> pageUserIds = cacheDataDeletedAccount.getRight().subList(startIndex, endIndex);
        return userProfileRepository.findSSoIdUserByIdIn(pageUserIds);
    }

    public List<String> getAllUserWithLeagueInvitationActivity(LocalDateTime changedAfter, Pageable pageable, String requestUrl, String externalClient) throws InvalidOperationException, FormatException, EntityNotExistException {
        log.info("getAllUserWithLeagueInvitationActivity: changedAfter: " + changedAfter + " pageable: " + pageable + " client: " + externalClient);
        boolean isRateLimitingActive = parameterService.getAsBoolean(PARAM_STAT_RATE_LIMITING_ACTIVE, DEFAULT_PARAM_STAT_RATE_LIMITING_ACTIVE, SYSTEM_USERNAME);
        rateLimitMinutes = parameterService.getAsInteger(ParameterDefaults.PARAM_STAT_RATE_LIMIT_MINUTES, ParameterDefaults.DEFAULT_PARAM_STAT_RATE_LIMIT_MINUTES, SYSTEM_USERNAME);
        forceRefreshCacheAfterMinutes = parameterService.getAsInteger(ParameterDefaults.PARAM_FORCE_REFRESH_CACHE_AFTER_MINUTES, ParameterDefaults.DEFAULT_PARAM_FORCE_REFRESH_CACHE_AFTER_MINUTES, SYSTEM_USERNAME);
        maxStatPageSize = parameterService.getAsInteger(ParameterDefaults.PARAM_MAX_STAT_ACTIVITY_PAGE_SIZE, ParameterDefaults.DEFAULT_MAX_STAT_ACTIVITY_PAGE_SIZE, SYSTEM_USERNAME);
        // Find the existing request for this client and URL
        Optional<HmClientRequest> existingRequestOpt = clientRequestRepository.findByRequestAndExternalClient(requestUrl, externalClient);

        if (isRateLimitingActive) {
            rateLimitHandler.checkRateLimit(changedAfter, pageable, requestUrl, externalClient, existingRequestOpt, rateLimitMinutes);
        }
        LocalDateTime now = LocalDateTime.now();
        boolean forceRefreshCache = !isNull(cacheDataInvitedManager.getLeft()) && now.isAfter(cacheDataInvitedManager.getLeft().plusMinutes(forceRefreshCacheAfterMinutes));

        // Check if we need to refresh the cache based on multiple conditions:
        // 1. Cache hasn't been initialized yet (cacheData.getLeft() == null) -> User Ids list is empty
        // 2. Filter parameter changed (cacheData.getMiddle() != changedAfter) -> changeStart-date was changed
        // 3. forceRefreshCache if "forceRefreshCacheAfterMinutes" minutes passed -> Cache expiration time (in minutes)
        boolean refreshCache = cacheDataInvitedManager.getLeft() == null || cacheDataInvitedManager.getMiddle() == null || !Objects.equals(cacheDataInvitedManager.getMiddle(), changedAfter) || forceRefreshCache;

        // Refresh the cache if needed
        if (refreshCache) {
            log.info("getAllUserWithLeagueInvitationActivity: refreshing the cache for users who have invited a manager since: " + changedAfter);
            List<String> userIds;
            userIds = hmLeagueInvitationRepository.findAllUserWithLeagueInvitationActivityAfter(changedAfter);
            List<UUID> uuids = userIds.stream()
                    .map(UUID::fromString)
                    .toList();
            cacheDataInvitedManager = Triple.of(LocalDateTime.now(), changedAfter, uuids);
        }

        // If pageable size is > maxStatPageSize, use maxStatPageSize instead
        int pageSize = pageable.getPageSize() > maxStatPageSize ? maxStatPageSize : pageable.getPageSize();
        int pageNumber = pageable.getPageNumber();
        // Calculate pagination
        int startIndex = pageNumber * pageSize;
        int endIndex = Math.min(startIndex + pageSize, cacheDataInvitedManager.getRight().size());

        // Check if the requested page is valid
        if (startIndex >= cacheDataInvitedManager.getRight().size()) {
            return Collections.emptyList(); // Return empty list if page is out of bounds
        }
        List<UUID> pageUserIds = cacheDataInvitedManager.getRight().subList(startIndex, endIndex);
        return userProfileRepository.findSSoIdUserByIdIn(pageUserIds);
    }

    @Transactional(readOnly = true)
    public String generateLineupActivityCsv() {
        log.info("Generating CSV export for lineup activity");
        List<Object[]> activityData = hmLineupRepository.findAllUserLineupActivityForCsvExport();
        return convertActivityDataToCsv(activityData);
    }

    @Transactional(readOnly = true)
    public String generateTransferMarketActivityCsv() {
        log.info("Generating CSV export for transfer market activity");
        List<Object[]> activityData = hmTransferMarketRepository.findAllUserTransferMarketActivityForCsvExport();
        return convertActivityDataToCsv(activityData);
    }

    @Transactional(readOnly = true)
    public String generatePlayerSalesActivityCsv() {
        log.info("Generating CSV export for player sales activity");
        List<Object[]> activityData = hmTransferMarketRepository.findAllUserPlayerSalesActivityForCsvExport();
        return convertActivityDataToCsv(activityData);
    }

    @Transactional(readOnly = true)
    public String generateSubmittedOfferActivityCsv() {
        log.info("Generating CSV export for submitted offer activity");
        List<Object[]> activityData = hmTransferMarketBidRepository.findAllUserSubmittedOfferActivityForCsvExport();
        return convertActivityDataToCsv(activityData);
    }

    @Transactional(readOnly = true)
    public String generateLeagueInvitationActivityCsv() {
        log.info("Generating CSV export for league invitation activity");
        List<Object[]> activityData = hmLeagueInvitationRepository.findAllUserLeagueInvitationActivityForCsvExport();
        return convertActivityDataToCsv(activityData);
    }

    private String convertActivityDataToCsv(List<Object[]> activityData) {
        StringBuilder csvBuilder = new StringBuilder();

        // Add CSV header
        csvBuilder.append("SSO_ID,last_activity_at\n");

        // Add data rows
        for (Object[] row : activityData) {
            String ssoId = (String) row[0];
            LocalDateTime lastActivityAt = (LocalDateTime) row[1];

            // Format timestamp without microseconds (e.g., 2025-07-10T07:43:39)
            String formattedTimestamp = lastActivityAt.format(DateTimeFormatter.ISO_LOCAL_DATE_TIME);

            csvBuilder.append(String.format("\"%s\",\"%s\"\n", ssoId, formattedTimestamp));
        }

        return csvBuilder.toString();
    }
}
