package com.pass.hbl.manager.backend.persistence.repository.hm;

import com.pass.hbl.manager.backend.persistence.domain.hm.HmUserAwardCreatedDO;
import com.pass.hbl.manager.backend.persistence.domain.hm.HmUserAwardDO;
import com.pass.hbl.manager.backend.persistence.domain.hm.UserAwardInfo;
import com.pass.hbl.manager.backend.persistence.dto.hm.AwardCode;
import com.pass.hbl.manager.backend.persistence.entity.hm.HmUserAward;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.PagingAndSortingRepository;
import org.springframework.data.repository.query.Param;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;

public interface HmUserAwardRepository extends PagingAndSortingRepository<HmUserAward, UUID> {

    Iterable<HmUserAward> findByUserProfileIdAndAwardIdAndLeagueId(UUID userId, UUID awardId, UUID leagueID);

    @Query(value = "select Cast(id as varchar) from hm.user_award where user_id = :userId and award_id = :awardId and league_id = :leagueId and deleted = false limit 1", nativeQuery = true)
    Optional<String> findIdByUserIdAndAwardIdAndLeagueId(UUID userId, UUID awardId, UUID leagueId);

    @Query(value = "select Cast(id as varchar) as id,Cast(last_round_id as varchar) as lastRoundId from hm.user_award where user_id = :userId and award_id = :awardId and league_id = :leagueId and deleted = false limit 1", nativeQuery = true)
    Optional<UserAwardInfo> findIdAndRoundByUserIdAndAwardIdAndLeagueId(UUID userId, UUID awardId, UUID leagueId);

    @Query(value = "select distinct(Cast(award_id as varchar)) from hm.user_award where user_id = :userId and award_id in :awardIds and league_id = :leagueId and deleted = false", nativeQuery = true)
    List<String> findAwardIdsByUserIdAndAwardIdAndLeagueId(UUID userId, Set<UUID> awardIds, UUID leagueId);

    @Query(value = "select distinct(Cast(league_id as varchar)) from hm.user_award where award_id in :awardIds and last_round_id = :roundId and deleted = false", nativeQuery = true)
    Set<String> findLeagueIdsByLastRoundIdAndAwardIdIn(UUID roundId, List<UUID> awardIds);

    @Query(value = "select Cast(user_id as varchar) as userId, Cast(league_id as varchar) as leagueId, Cast(award_id as varchar) as awardId from hm.user_award where award_id in :awardIds and last_round_id = :roundId and deleted = false", nativeQuery = true)
    List<HmUserAwardDO> findUserAwardByLastRoundIdAndAwardIdIn(UUID roundId, List<UUID> awardIds);

    @Query(value = "select Cast(id as varchar) from hm.user_award where user_id = :userId and award_id = :awardId and deleted = false", nativeQuery = true)
    Optional<String> findIdByUserIdAndAwardId(UUID userId, UUID awardId);

    @Query(value = "select * from hm.user_award where user_id = :userId and (league_id = :leagueId or league_id is null) and deleted = false", nativeQuery = true)
    List<HmUserAward> findByUserIdAndLeagueId(UUID userId, UUID leagueId);

    @Query(value = "select Cast(user_id as varchar) as userId, Cast(league_id as varchar) as leagueId, " +
            "Cast(award_id as varchar) as awardId, created_at as createdAt from hm.user_award where user_id = :userId and league_id = :leagueId " +
            "and created_at > :sinceDate and deleted = false", nativeQuery = true)
    List<HmUserAwardCreatedDO> findByUserIdAndLeagueIdSinceDate(UUID userId, UUID leagueId, LocalDateTime sinceDate);

    @Query("select u from HmUserAward u where (u.league.id = :leagueId or u.league.id = :previousLeagueId ) and u.award.code in :codes and u.deleted = false")
    List<HmUserAward> findLeagueAwards(@Param("leagueId") UUID leagueId, @Param("previousLeagueId") UUID previousLeagueId, @Param("codes") List<AwardCode> codes);

    @Query("select u from HmUserAward u where u.league.id = :leagueId and u.award.code in :codes and u.deleted = false")
    List<HmUserAward> findLeagueAwards(@Param("leagueId") UUID leagueId, @Param("codes") List<AwardCode> codes);

    @Query("select u from HmUserAward u where u.userProfile.id = :userId and u.league.id in :previousLeagueIds and u.award.code = :code and u.deleted = false")
    List<HmUserAward> findAwardByUserInPreviousLeagues(@Param("userId") UUID userId, @Param("previousLeagueIds") List<UUID> previousLeagueIds, @Param("code") AwardCode code);

    @Modifying
    @Query("update HmUserAward u set u.deleted = true, u.deletedAt = CURRENT_TIMESTAMP where u.award.id = :awardId and u.deleted = false")
    void deleteByAward(@Param("awardId") UUID awardId);

    @Modifying
    @Query("update HmUserAward u set u.deleted = true, u.deletedAt = CURRENT_TIMESTAMP where u.league.id = :leagueId and u.deleted = false")
    void deleteByLeague(@Param("leagueId") UUID leagueId);

    @Modifying
    @Query("update HmUserAward u set u.deleted = true, u.deletedAt = CURRENT_TIMESTAMP where u.league.id = :leagueId and u.userProfile.id = :userId and u.deleted = false")
    void deleteByLeagueIdAndUserId(@Param("leagueId") UUID leagueId, @Param("userId") UUID userId);

    @Modifying
    @Query(value = "update hm.user_award set deleted = false, deleted_at = '1970-01-01 00:00:00' where league_id in :leagueIds and user_id = :userId and deleted = true", nativeQuery = true)
    int activateAwardsByUserIdAndLeagueIdsIn(@Param("userId") UUID userId, @Param("leagueIds") List<UUID> leagueIds);

    @Modifying
    @Query("update HmUserAward u set u.numberOfAchievements = u.numberOfAchievements + 1, u.modifiedAt = CURRENT_TIMESTAMP where u.id = :userAwardId and u.deleted = false")
    void incrementNumberOfAchievements(UUID userAwardId);

    @Modifying
    @Query("update HmUserAward u set u.numberOfAchievements = u.numberOfAchievements + 1, u.lastRound.id = :roundId, u.modifiedAt = CURRENT_TIMESTAMP where u.id = :userAwardId and u.deleted = false")
    void incrementNumberOfAchievements(UUID userAwardId, UUID roundId);

    @Query("select u.currentSeries from HmUserAward u where u.id = :userAwardId")
    int findCurrentSeriesByUserAwardId(@Param("userAwardId") UUID userAwardId);

    @Modifying
    @Query("update HmUserAward u set u.currentSeries = u.currentSeries + 1, u.lastRound.id = :roundId, u.modifiedAt = CURRENT_TIMESTAMP where u.id = :userAwardId and u.deleted = false")
    void incrementNumberOfCurrentSeries(UUID userAwardId, UUID roundId);

    @Modifying
    @Query("update HmUserAward u set u.currentSeries = :currentSeries, u.lastRound.id = :roundId, u.modifiedAt = CURRENT_TIMESTAMP where u.id = :userAwardId and u.deleted = false")
    void updateNumberOfCurrentSeries(int currentSeries,UUID userAwardId, UUID roundId);

    @Modifying
    @Query("update HmUserAward u set u.deleted = true, u.deletedAt = CURRENT_TIMESTAMP where u.userProfile.id = :userId and u.deleted = false")
    void deleteByUserId(UUID userId);
}
